# **STAGE 1: Build the C Program**
FROM registry.access.redhat.com/ubi9/ubi:latest as build

# Mettre à jour le système et installer les outils nécessaires
RUN echo "Updating system" && dnf update -y && \
    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
    dnf install -y dnf-plugins-core && \
    dnf clean all

# Activation des dépôts supplémentaires
RUN echo "Activating repository" && \
    dnf config-manager --set-enabled ubi-9-baseos-rpms ubi-9-appstream-rpms

# Installation des dépendances nécessaires pour la compilation
RUN echo "Installing build dependencies" && \
    dnf install -y \
        gcc \
        gcc-c++ \
        make \
        cmake \
        autoconf \
        automake \
        libtool \
        pkgconfig \
        zlib-devel \
        cjson-devel \
        openssl-devel \
        libssh2-devel && \
    dnf clean all

# Vérifier l'installation de CMake
RUN cmake --version

# Création du workdir
WORKDIR /app

# Copie du fichier source
COPY demog_updater.tar.gz ./

# Decompresse et install
RUN tar -xzvf demog_updater.tar.gz && \
    rm demog_updater.tar.gz && \
    mv demog_updater/* . && \
    rm -rf demog_updater

# Compilation avec préfixe d'installation personnalisé
RUN echo "Starting compilation process" && \
    mkdir build && cd build && \
    echo "Build directory created, starting cmake" && \
    cmake .. && \
    echo "CMAKE completed, starting make" && \
    make && \
    echo "Make completed" && \
    make install && \
    echo "Installation completed"

# **STAGE 2: Runtime Container**
FROM registry.access.redhat.com/ubi9/ubi-init

LABEL maintainer="Synlab IT Team" \
      description="Demog Updater Service" \
      version="1.0"

# Installation des dépendances runtime uniquement
RUN echo "Updating system" && dnf update -y && dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
    dnf install -y zlib cjson openssl-libs libssh2 && \
    dnf clean all

# Copier les fichiers installés depuis le stage de build
COPY --from=build /opt/demog_updater/ /opt/demog_updater/

# Créer les répertoires de données avec les bonnes permissions
# Note: Les données seront montées depuis l'host via volume
RUN mkdir -p /opt/demog_updater/var/data && \
    mkdir -p /opt/demog_updater/var/logs && \
    chmod 755 /opt/demog_updater/bin/demog_updater && \
    chmod +x /opt/demog_updater/bin/demog_updater && \
    chmod 755 /opt/demog_updater/var/data && \
    chmod 755 /opt/demog_updater/var/logs

# Point d'entrée du conteneur
# ENTRYPOINT ["/opt/demog_updater/bin/demog_updater", "--config", "/opt/demog_updater/etc/config.json"]

# Métadonnées pour les volumes
LABEL ports.none="No ports exposed" \
      volumes.data="/opt/demog_updater/var/data" \
      volumes.logs="/opt/demog_updater/var/logs" \
      usage="podman run -d --name demogupdater_service -v /opt/shared-data:/opt/demog_updater/var/data:Z -v /var/log/demogupdater/:/opt/demog_updater/var/logs:Z demog_updater:latest"

# Documentation des volumes attendus
VOLUME ["/opt/demog_updater/var/data", "/opt/demog_updater/var/logs"]