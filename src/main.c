#include "update.h"
#include "sftp_manager.h"  // Nouveau
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <stdbool.h>
#include <time.h>
#include <sys/stat.h>
#include <string.h>
#include "core/logger.h"
#include <cjson/cJSON.h>
#include <errno.h>

// Déclaration de la fonction read_file_content
char* read_file_content(const char *file_path);


// Variables globales pour la gestion des signaux
static volatile sig_atomic_t keep_running = 1;

LogLevel parse_log_level(const char* level_str) {
    if (strcasecmp(level_str, "DEBUG") == 0) return LOG_DEBUG;
    if (strcasecmp(level_str, "INFO") == 0) return LOG_INFO;
    if (strcasecmp(level_str, "WARNING") == 0) return LOG_WARNING;
    if (strcasecmp(level_str, "ERROR") == 0) return LOG_ERROR;
    if (strcasecmp(level_str, "CRITICAL") == 0) return LOG_CRITICAL;
    return LOG_INFO; // Par défaut
}

// Structure pour la configuration
typedef struct {
    char local_data_path[256];
    char shared_dir_path[256];
    char log_dir_path[256];
    LogLevel log_level;
    int sleep_duration;
    int operating_days[7];
    int start_time;
    int end_time;
    char local_version_filename[64];
    char remote_version_filename[64];
    char demographics_filename[128];
    char index_iep_filename[128];
    char index_ipp_filename[128];
    char index_numss_filename[128];
    SftpConfig sftp;  // Nouveau
} Config;

// Fonction pour charger la configuration SFTP
SftpConfig load_sftp_config(cJSON *json) {
    logger_log(LOG_DEBUG, "main.c : Chargement de la configuration SFTP...");
    SftpConfig sftp_config = {0};
    
    cJSON *sftp_obj = cJSON_GetObjectItemCaseSensitive(json, "sftp");
    if (!sftp_obj) {
        logger_log(LOG_WARNING, "main.c : Section SFTP manquante dans la configuration, SFTP désactivé");
        sftp_config.enabled = false;
        return sftp_config;
    }
    
    // Enabled
    cJSON *enabled = cJSON_GetObjectItemCaseSensitive(sftp_obj, "enabled");
    sftp_config.enabled = cJSON_IsTrue(enabled);
    
    if (!sftp_config.enabled) {
        logger_log(LOG_INFO, "main.c : SFTP désactivé dans la configuration");
        return sftp_config;
    }
    
    // Host
    cJSON *host = cJSON_GetObjectItemCaseSensitive(sftp_obj, "host");
    if (cJSON_IsString(host)) {
        strncpy(sftp_config.host, host->valuestring, sizeof(sftp_config.host) - 1);
    } else {
        logger_log(LOG_ERROR, "main.c : Host SFTP manquant ou invalide");
        sftp_config.enabled = false;
        return sftp_config;
    }
    
    // Port
    cJSON *port = cJSON_GetObjectItemCaseSensitive(sftp_obj, "port");
    sftp_config.port = cJSON_IsNumber(port) ? port->valueint : 22;
    
    // Username
    cJSON *username = cJSON_GetObjectItemCaseSensitive(sftp_obj, "username");
    if (cJSON_IsString(username)) {
        strncpy(sftp_config.username, username->valuestring, sizeof(sftp_config.username) - 1);
    } else {
        logger_log(LOG_ERROR, "main.c : Username SFTP manquant ou invalide");
        sftp_config.enabled = false;
        return sftp_config;
    }
    
    // Password
    cJSON *password = cJSON_GetObjectItemCaseSensitive(sftp_obj, "password");
    if (cJSON_IsString(password)) {
        strncpy(sftp_config.password, password->valuestring, sizeof(sftp_config.password) - 1);
    }
    
    // Private key path
    cJSON *private_key = cJSON_GetObjectItemCaseSensitive(sftp_obj, "private_key_path");
    if (cJSON_IsString(private_key)) {
        strncpy(sftp_config.private_key_path, private_key->valuestring, sizeof(sftp_config.private_key_path) - 1);
    }
    
    // Remote base path
    cJSON *remote_path = cJSON_GetObjectItemCaseSensitive(sftp_obj, "remote_base_path");
    if (cJSON_IsString(remote_path)) {
        strncpy(sftp_config.remote_base_path, remote_path->valuestring, sizeof(sftp_config.remote_base_path) - 1);
    } else {
        strcpy(sftp_config.remote_base_path, "/");
    }

    // Delta filename pattern (à ajouter ici)
    cJSON *delta_pattern = cJSON_GetObjectItemCaseSensitive(sftp_obj, "delta_filename_pattern");
    if (cJSON_IsString(delta_pattern)) {
        strncpy(sftp_config.delta_filename_pattern, delta_pattern->valuestring, sizeof(sftp_config.delta_filename_pattern) - 1);
    } else {
        // Valeur par défaut si besoin
        strcpy(sftp_config.delta_filename_pattern, "demographics_v%d_v%d.csv.gz");
    }
    
    // Timeouts et retry
    cJSON *conn_timeout = cJSON_GetObjectItemCaseSensitive(sftp_obj, "connection_timeout");
    sftp_config.connection_timeout = cJSON_IsNumber(conn_timeout) ? conn_timeout->valueint : 30;
    
    cJSON *transfer_timeout = cJSON_GetObjectItemCaseSensitive(sftp_obj, "transfer_timeout");
    sftp_config.transfer_timeout = cJSON_IsNumber(transfer_timeout) ? transfer_timeout->valueint : 300;
    
    cJSON *retry_attempts = cJSON_GetObjectItemCaseSensitive(sftp_obj, "retry_attempts");
    sftp_config.retry_attempts = cJSON_IsNumber(retry_attempts) ? retry_attempts->valueint : 3;
    
    cJSON *retry_delay = cJSON_GetObjectItemCaseSensitive(sftp_obj, "retry_delay");
    sftp_config.retry_delay = cJSON_IsNumber(retry_delay) ? retry_delay->valueint : 5;
    
    logger_log(LOG_INFO, "main.c : Configuration SFTP chargée - Host: %s, Port: %d, User: %s", 
              sftp_config.host, sftp_config.port, sftp_config.username);
    
    return sftp_config;
}

// Fonction pour charger la configuration
Config load_config(const char *config_path) {
    logger_log(LOG_DEBUG, "main.c : Starting load_config...");
    Config config = {0};
    char *content = read_file_content(config_path);
    if (content == NULL) {
        logger_log(LOG_ERROR, "main.c : Impossible de lire le fichier de configuration.");
        exit(1);
    }

    cJSON *json = cJSON_Parse(content);
    if (json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            logger_log(LOG_ERROR, "main.c : Erreur de parsing JSON: %s", error_ptr);
        }
        free(content);
        exit(1);
    }

    strcpy(config.local_data_path, cJSON_GetObjectItemCaseSensitive(json, "local_data_path")->valuestring);
    strcpy(config.shared_dir_path, cJSON_GetObjectItemCaseSensitive(json, "shared_dir_path")->valuestring);
    strcpy(config.log_dir_path, cJSON_GetObjectItemCaseSensitive(json, "log_dir_path")->valuestring);

    // Récupération des noms de fichiers de version
    logger_log(LOG_DEBUG, "main.c : Getting name of version files...");
    cJSON *local_version_item = cJSON_GetObjectItemCaseSensitive(json, "local_version_filename");
    if (cJSON_IsString(local_version_item)) {
        strncpy(config.local_version_filename, local_version_item->valuestring, sizeof(config.local_version_filename) - 1);
    } else {
        strcpy(config.local_version_filename, "local_version.json"); // Valeur par défaut
    }
    cJSON *remote_version_item = cJSON_GetObjectItemCaseSensitive(json, "remote_version_filename");
    if (cJSON_IsString(remote_version_item)) {
        strncpy(config.remote_version_filename, remote_version_item->valuestring, sizeof(config.remote_version_filename) - 1);
    } else {
        strcpy(config.remote_version_filename, "remote_version.json"); // Valeur par défaut
    }

    // Récupération du nom du fichier de démographie
    logger_log(LOG_DEBUG, "main.c : Getting name of demographics file...");
    cJSON *demographics_item = cJSON_GetObjectItemCaseSensitive(json, "demographics_filename");
    if (cJSON_IsString(demographics_item)) {
        strncpy(config.demographics_filename, demographics_item->valuestring, sizeof(config.demographics_filename) - 1);
    } else {
        strcpy(config.demographics_filename, "demographics.csv"); // Valeur par défaut
    }

    // Ajout des fichiers indexes
    cJSON *index_files = cJSON_GetObjectItemCaseSensitive(json, "index_files");
    if (index_files) {
        cJSON *iep = cJSON_GetObjectItemCaseSensitive(index_files, "iep");
        if (cJSON_IsString(iep)) strncpy(config.index_iep_filename, iep->valuestring, sizeof(config.index_iep_filename)-1);
        cJSON *ipp = cJSON_GetObjectItemCaseSensitive(index_files, "ipp");
        if (cJSON_IsString(ipp)) strncpy(config.index_ipp_filename, ipp->valuestring, sizeof(config.index_ipp_filename)-1);
        cJSON *numss = cJSON_GetObjectItemCaseSensitive(index_files, "numss");
        if (cJSON_IsString(numss)) strncpy(config.index_numss_filename, numss->valuestring, sizeof(config.index_numss_filename)-1);
    }

    // Ajout du niveau de log
    cJSON *log_level_item = cJSON_GetObjectItemCaseSensitive(json, "log_level");
    if (cJSON_IsString(log_level_item)) {
        config.log_level = parse_log_level(log_level_item->valuestring);
    } else {
        config.log_level = LOG_INFO; // Par défaut
    }
    config.sleep_duration = cJSON_GetObjectItemCaseSensitive(json, "sleep_duration")->valueint;

    cJSON *days = cJSON_GetObjectItemCaseSensitive(json, "operating_days");
    cJSON *day;
    int i = 0;
    cJSON_ArrayForEach(day, days) {
        config.operating_days[i++] = day->valueint;
    }

    char *start_time_str = cJSON_GetObjectItemCaseSensitive(json, "start_time")->valuestring;
    char *end_time_str = cJSON_GetObjectItemCaseSensitive(json, "end_time")->valuestring;
    int start_hour, start_min, end_hour, end_min;
    sscanf(start_time_str, "%d:%d", &start_hour, &start_min);
    sscanf(end_time_str, "%d:%d", &end_hour, &end_min);
    config.start_time = start_hour * 60 + start_min;
    config.end_time = end_hour * 60 + end_min;

    // Chargement de la configuration SFTP
    config.sftp = load_sftp_config(json);

    cJSON_Delete(json);
    free(content);
    return config;
}

// Fonction pour vérifier si le jour est un jour de fonctionnement
bool is_operating_day(int day, const Config *config) {
    logger_log(LOG_DEBUG,"main.c : calling is_operating_day...");
    for (int i = 0; i < 7; i++) {
        if (config->operating_days[i] == day) {
            logger_log(LOG_DEBUG,"main.c : is_operating_day = true");
            return true;
        }
    }
    logger_log(LOG_DEBUG,"main.c : is_operating_day = false");
    return false;
}

// Fonction pour gérer les signaux
void handle_signal(int sig) {
    keep_running = 0;
}

#define VERSION "1.0.20250814.1152"

int main(int argc, char *argv[]) {
    if (argc != 2) {
        fprintf(stderr, "Usage: %s <config_file>\n", argv[0]);
        return 1;
    }

    Config config = load_config(argv[1]);

    // Création du dossier de logs s'il n'existe pas
    struct stat st_log;
    if (stat(config.log_dir_path, &st_log) != 0) {
        if (mkdir(config.log_dir_path, 0755) != 0) {
            fprintf(stderr, "Erreur lors de la création du dossier de logs '%s': %s\n", config.log_dir_path, strerror(errno));
            return 1;
        }
    } else if (!S_ISDIR(st_log.st_mode)) {
        fprintf(stderr, "'%s' existe mais n'est pas un dossier.\n", config.log_dir_path);
        return 1;
    }

    // Création du dossier shared s'il n'existe pas
    struct stat st_shared;
    if (stat(config.shared_dir_path, &st_shared) != 0) {
        if (mkdir(config.shared_dir_path, 0755) != 0) {
            fprintf(stderr, "Erreur lors de la création du dossier partagé '%s': %s\n", config.shared_dir_path, strerror(errno));
            return 1;
        }
        logger_log(LOG_INFO, "main.c : Dossier partagé créé: %s", config.shared_dir_path);
    } else if (!S_ISDIR(st_shared.st_mode)) {
        fprintf(stderr, "'%s' existe mais n'est pas un dossier.\n", config.shared_dir_path);
        return 1;
    }

    if (logger_init(config.log_dir_path, "update_service", config.log_level) != 0) {
        printf("Erreur lors de l'initialisation du logger.\n");
        return 1;
    }

    logger_log(LOG_INFO,"Starting demog_updater version %s", VERSION);

    struct stat st;
    if (stat(config.local_data_path, &st) != 0) {
        logger_log(LOG_ERROR, "main.c : Le chemin local des données est inaccessible: %s", config.local_data_path);
        return 1;
    }

    signal(SIGINT, handle_signal);
    signal(SIGTERM, handle_signal);

    logger_log(LOG_INFO, "main.c : Démarrage du service de mise à jour de la démographie...");
    if (config.sftp.enabled) {
        logger_log(LOG_INFO, "main.c : SFTP activé - Serveur: %s:%d", config.sftp.host, config.sftp.port);
    } else {
        logger_log(LOG_INFO, "main.c : SFTP désactivé - Mode local uniquement");
    }

    while (keep_running) {
        time_t now;
        time(&now);
        struct tm *local = localtime(&now);
        int current_day = local->tm_wday;
        int current_time = local->tm_hour * 60 + local->tm_min;

        logger_log(LOG_DEBUG, "main.c : Test de la plage horaire - Jour actuel: %d, Heure actuelle: %02d:%02d",
                    current_day, local->tm_hour, local->tm_min);
        if (is_operating_day(current_day, &config) && current_time >= config.start_time && current_time <= config.end_time) {
            logger_log(LOG_INFO, "main.c : Début du cycle de mise à jour...");
            
            // 1. Synchronisation du fichier de version distant si SFTP activé
            if (!config.sftp.enabled) {
                logger_log(LOG_INFO, "main.c : SFTP désactivé - Mode local uniquement");
            } else {
                logger_log(LOG_INFO, "main.c : Synchronisation du fichier de version depuis le serveur SFTP...");
                int sync_result = sftp_sync_remote_version(&config.sftp, config.shared_dir_path, config.remote_version_filename);
                if (sync_result != 0) {
                    logger_log(LOG_ERROR, "main.c : Échec de la synchronisation SFTP du fichier de version");
                } else {
                    logger_log(LOG_INFO, "main.c : Fichier de version %s synchronisé avec succès.", config.remote_version_filename);
                    // 2. Vérification et application des mises à jour
                    logger_log(LOG_INFO, "main.c : Vérification des mises à jour...");
                    int result = run_update_process_with_sftp(config.local_data_path, 
                                                            config.shared_dir_path, 
                                                            config.local_version_filename, 
                                                            config.remote_version_filename, 
                                                            config.demographics_filename, 
                                                            config.index_iep_filename, 
                                                            config.index_ipp_filename, 
                                                            config.index_numss_filename,
                                                            &config.sftp);
                    if (result == 0) {
                        logger_log(LOG_INFO, "main.c : Mise à jour terminée avec succès ou non nécessaire.");
                    } else {
                        logger_log(LOG_ERROR, "main.c : Erreur lors de la mise à jour (code %d).", result);
                    }
                }
            }
        } else {
            logger_log(LOG_INFO, "main.c : Hors des heures de fonctionnement ou jour non opérationnel.");
        }

        logger_log(LOG_INFO, "main.c : Mise en veille pour %d secondes...", config.sleep_duration);
        sleep(config.sleep_duration);
    }

    logger_log(LOG_INFO, "main.c : Arrêt du service de mise à jour de la démographie.");
    logger_cleanup();
    return 0;
}