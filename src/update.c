#include "update.h"
#include "sftp_manager.h"  // Nouveau
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>      // Pour gérer les timestamps
#include <zlib.h>      // Pour la décompression Gzip
#include <sys/stat.h>  // Pour vérifier l'existence de fichiers
#include <cjson/cJSON.h>
#include "core/logger.h" // Ajout pour la journalisation
#include <errno.h>     // Pour les erreurs système
#include <limits.h>    // Pour PATH_MAX
#include <unistd.h>    // Pour realpath et getcwd

// Définir PATH_MAX si non défini
#ifndef PATH_MAX
#define PATH_MAX 4096
#endif

// Déclarations des prototypes des fonctions internes (static)
char* read_file_content(const char *file_path);
char* read_gz_file_content(const char *file_path);
static LocalVersion load_local_version(const char *path);
static int save_local_version(const char *path, LocalVersion version);
static RemoteVersion parse_remote_version(const char *json_string);
static int apply_demographics_delta(const char *delta_content, const char *data_dir, const char *demographics_filename);
static int decompress_gzip_content(const char *compressed_data, size_t compressed_size, char **decompressed_data, size_t *decompressed_size);
static int upload_local_version_to_sftp(const SftpConfig *sftp_config,
                                        const char *local_version_path,
                                        const char *local_version_filename,
                                        const char *box_serial_number);

/**
 * @brief Upload le fichier local_version.json vers le serveur SFTP avec gestion complète
 * @param sftp_config Configuration SFTP
 * @param local_version_path Chemin complet du fichier local_version.json
 * @param local_version_filename Nom du fichier (ex: "local_version.json")
 * @param box_serial_number Numéro de série de la box
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
static int upload_local_version_to_sftp(const SftpConfig *sftp_config, 
                                       const char *local_version_path,
                                       const char *local_version_filename,
                                       const char *box_serial_number) {
    if (!sftp_config || !sftp_config->enabled) {  //TODO a revoir car normalement pas besoin le test est fait en amont
        logger_log(LOG_DEBUG, "update.c : SFTP désactivé, pas d'upload de local_version.json");
        return 0;
    }
    
    logger_log(LOG_DEBUG, "update.c : Upload du fichier local_version.json vers le serveur SFTP...");
    
    SftpSession *session = sftp_session_init(sftp_config);
    if (!session) {
        logger_log(LOG_ERROR, "update.c : Impossible d'établir la session SFTP pour l'upload.");
        return -1;
    }
    
    char remote_renamed_path[1024];
    int result = sftp_upload_file_with_rename(session, local_version_path, 
                                            local_version_filename, box_serial_number,
                                            remote_renamed_path, sizeof(remote_renamed_path));
    
    if (result == 0) {
        logger_log(LOG_INFO, "update.c : Fichier local_version.json uploadé avec succès vers %s", remote_renamed_path);
    } else {
        logger_log(LOG_ERROR, "update.c : Échec du transfert de local_version.json vers le serveur SFTP.");
    }
    
    sftp_session_cleanup(session);
    return result;
}

/**
 * @brief Point d'entrée de la logique de mise à jour avec support SFTP.
 * Cette fonction orchestre le processus complet de vérification et d'application
 * des mises à jour de la démographie, incluant le téléchargement via SFTP si nécessaire.
 */
int run_update_process_with_sftp(const char *local_data_dir, const char *shared_dir, 
                                const char *local_version_filename, const char *remote_version_filename, 
                                const char *demographics_filename, const char *index_iep_filename, 
                                const char *index_ipp_filename, const char *index_numss_filename,
                                const SftpConfig *sftp_config) {
    logger_log(LOG_DEBUG, "update.c : Début de run_update_process_with_sftp");
    
    char local_version_path[512];
    snprintf(local_version_path, sizeof(local_version_path), "%s/%s", local_data_dir, local_version_filename);

    char remote_version_path[512];
    snprintf(remote_version_path, sizeof(remote_version_path), "%s/%s", shared_dir, remote_version_filename);

    logger_log(LOG_DEBUG, "update.c : Chargement de la version locale depuis %s", local_version_path);
    LocalVersion local_v = load_local_version(local_version_path);
    if (local_v.current_version == -1) {
        logger_log(LOG_WARNING, "update.c : Fichier local_version.json introuvable ou invalide. Initialisation requise.");
        // Gérer le cas d'une première exécution : télécharger le snapshot initial
        return -1;
    }

    logger_log(LOG_DEBUG, "update.c : Lecture du fichier version.json depuis %s", remote_version_path);
    char *remote_json_content = read_file_content(remote_version_path);
    if (remote_json_content == NULL) {
        logger_log(LOG_ERROR, "update.c : Impossible de lire le fichier version.json depuis le répertoire partagé.");
        return -1;
    }
    
    RemoteVersion remote_v = parse_remote_version(remote_json_content);
    if (remote_v.current_version <= local_v.current_version) {
        logger_log(LOG_INFO, "update.c : La version locale (%d) est à jour.", local_v.current_version);
        free(remote_json_content);
        return 0;
    }
    
    logger_log(LOG_DEBUG, "update.c : Mise à jour disponible : de la version %d à %d.", local_v.current_version, remote_v.current_version);
    
    int start_version = local_v.current_version;
    int end_version = remote_v.current_version;
    
    // Télécharger les deltas manquants via SFTP si activé
    if (sftp_config && sftp_config->enabled) {
        logger_log(LOG_DEBUG, "update.c : Téléchargement des deltas via SFTP...");
        if (sftp_download_deltas(sftp_config, shared_dir, start_version, end_version) != 0) {
            logger_log(LOG_ERROR, "update.c : Échec du téléchargement des deltas via SFTP");
            free(remote_json_content);
            return -1;
        }
    }
    
    // Application des deltas
    for (int i = start_version; i < end_version; i++) {
        char delta_filename[512];
        char delta_path[512];
        
        snprintf(delta_filename, sizeof(delta_filename), "demographics_v%d_v%d.csv.gz", i, i + 1);
        snprintf(delta_path, sizeof(delta_path), "%s/%s", shared_dir, delta_filename);

        logger_log(LOG_DEBUG, "update.c : Lecture et application du fichier delta %s...", delta_filename);
        char *delta_content_uncompressed = read_gz_file_content(delta_path);
        if (delta_content_uncompressed == NULL) {
            logger_log(LOG_ERROR, "update.c : Fichier delta %s introuvable. Arrêt de la mise à jour.", delta_filename);
            free(remote_json_content);
            return -1;
        }
        
        logger_log(LOG_DEBUG, "update.c : Application du delta %s...", delta_filename);
        if (apply_demographics_delta(delta_content_uncompressed, local_data_dir, demographics_filename) != 0) {
            logger_log(LOG_ERROR, "update.c : Erreur lors de l'application du delta %s.", delta_filename);
            free(delta_content_uncompressed);
            free(remote_json_content);
            return -1;
        }

        // Fin de l'upgrade
        logger_log(LOG_DEBUG, "update.c : Delta %s appliqué avec succès.", delta_filename);
        free(delta_content_uncompressed);
    }
    
    // 5. Mise à jour des fichiers d'index
    logger_log(LOG_DEBUG, "update.c : Mise à jour des fichiers d'index...");
    const char *index_filenames[3] = {index_iep_filename, index_ipp_filename, index_numss_filename };
    for (int idx_type = 0; idx_type < 3; idx_type++) {
        logger_log(LOG_DEBUG, "update.c : Reconstruction de l'index %s...", index_filenames[idx_type]);
        int status = rebuild_demographics_index(local_data_dir, demographics_filename, index_filenames[idx_type], idx_type);
        if (status != 0) {
            strcpy(local_v.last_status, "ERROR");
            save_local_version(local_version_path, local_v);
            logger_log(LOG_CRITICAL, "Arrêt du programme suite à une erreur de reconstruction d'index (%s).", index_filenames[idx_type]);
            free(remote_json_content);
            exit(EXIT_FAILURE);
        }
    }
    strcpy(local_v.last_status, "OK");
    logger_log(LOG_DEBUG, "update.c : Index mis à jour avec succès.");

    // 6. Mettre à jour le fichier de version local
    local_v.current_version = remote_v.current_version;
    time_t now = time(NULL);
    struct tm *local_time = localtime(&now);
    strftime(local_v.last_sync, sizeof(local_v.last_sync), "%Y-%m-%dT%H:%M:%S", local_time);
    logger_log(LOG_DEBUG, "update.c : Mise à jour de la version locale à %d, dernière synchronisation à %s", local_v.current_version, local_v.last_sync);
    
    if (save_local_version(local_version_path, local_v) != 0) {
        logger_log(LOG_ERROR, "update.c : Impossible de mettre à jour local_version.json.");
        free(remote_json_content);
        return -1;
    }

    logger_log(LOG_DEBUG, "update.c : Mise à jour terminée. La version locale est maintenant %d.", local_v.current_version);

    // 7. Transfert du fichier local_version.json vers le serveur SFTP
    const char *box_serial = "BOX_IATA_10"; // À revoir avec le boitier lecture du SN dans RPMB
    if (upload_local_version_to_sftp(sftp_config, local_version_path, 
                                   local_version_filename, box_serial) != 0) {
        logger_log(LOG_WARNING, "update.c : Upload du fichier local_version.json échoué, mais mise à jour locale réussie.");
        // Ne pas retourner d'erreur car la mise à jour locale a réussi
    }

    free(remote_json_content);
    return 0;
}

// Fonction pour charger et parser le fichier local_version.json
static LocalVersion load_local_version(const char *path) {
    logger_log(LOG_DEBUG, "update.c : Début de load_local_version");
    LocalVersion local_v = { .current_version = -1, .last_sync = "" }; // Initialisation

    char *content = read_file_content(path);
    if (content == NULL) {
        logger_log(LOG_WARNING, "update.c : Impossible de lire le fichier local_version.json (%s).", path);
        return local_v;
    }

    cJSON *json = cJSON_Parse(content);
    logger_log(LOG_DEBUG, "update.c : Parsing du fichier local_version.json");
    if (json != NULL) {
        cJSON *version_item = cJSON_GetObjectItemCaseSensitive(json, "current_version");
        if (cJSON_IsNumber(version_item)) {
            local_v.current_version = version_item->valueint;
        }
        cJSON *sync_item = cJSON_GetObjectItemCaseSensitive(json, "last_sync");
        if (cJSON_IsString(sync_item) && (strlen(sync_item->valuestring) < sizeof(local_v.last_sync))) {
            strcpy(local_v.last_sync, sync_item->valuestring);
        }
        cJSON *status_item = cJSON_GetObjectItemCaseSensitive(json, "last_status");
        if (cJSON_IsString(status_item) && (strlen(status_item->valuestring) < sizeof(local_v.last_status))) {
            strcpy(local_v.last_status, status_item->valuestring);
        }

    } else {
        logger_log(LOG_ERROR, "update.c : Erreur de parsing JSON pour local_version.json.");
    }
    
    cJSON_Delete(json);
    free(content);
    logger_log(LOG_DEBUG, "update.c : Chargement de la version locale terminé. Version actuelle: %d, Dernière synchronisation: %s, Dernier statut: %s",
                local_v.current_version, local_v.last_sync, local_v.last_status);
    return local_v;
}

// Fonction pour sauvegarder la structure LocalVersion dans un fichier
static int save_local_version(const char *path, LocalVersion version) {
    logger_log(LOG_DEBUG, "update.c : Début de save_local_version");

    // Validation du chemin pour prévenir les attaques de path traversal
    if (validate_file_path(path) != 0) {
        logger_log(LOG_ERROR, "update.c : Chemin de fichier invalide ou dangereux: %s", path);
        return -1;
    }

    cJSON *json = cJSON_CreateObject();
    if (json == NULL) {
        logger_log(LOG_ERROR, "update.c : Erreur d'allocation cJSON pour sauvegarde de la version locale.");
        return -1;
    }

    cJSON_AddNumberToObject(json, "current_version", version.current_version);
    cJSON_AddStringToObject(json, "last_sync", version.last_sync);
    cJSON_AddStringToObject(json, "last_status", version.last_status);

    char *json_string = cJSON_Print(json);
    if (json_string == NULL) {
        logger_log(LOG_ERROR, "update.c : Erreur lors de la conversion JSON en chaîne.");
        cJSON_Delete(json);
        return -1;
    }
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        logger_log(LOG_ERROR, "update.c : Impossible d'ouvrir %s en écriture.", path);
        free(json_string);
        cJSON_Delete(json);
        return -1;
    }

    fprintf(fp, "%s", json_string);
    fclose(fp);
    
    free(json_string);
    cJSON_Delete(json);
    logger_log(LOG_INFO, "update.c : Fichier local_version.json mis à jour (%s).", path);
    return 0;
}

// Fonction pour parser le fichier version.json
static RemoteVersion parse_remote_version(const char *json_string) {
    logger_log(LOG_DEBUG, "update.c : Début de parse_remote_version");
    RemoteVersion remote_v = { .current_version = -1, .snapshot = -1, .timestamp = "" };

    cJSON *json = cJSON_Parse(json_string);
    if (json != NULL) {
        cJSON *version_item = cJSON_GetObjectItemCaseSensitive(json, "current_version");
        if (cJSON_IsNumber(version_item)) {
            remote_v.current_version = version_item->valueint;
        }
        cJSON *snapshot_item = cJSON_GetObjectItemCaseSensitive(json, "snapshot");
        if (cJSON_IsNumber(snapshot_item)) {
            remote_v.snapshot = snapshot_item->valueint;
        }
        cJSON *timestamp_item = cJSON_GetObjectItemCaseSensitive(json, "timestamp");
        if (cJSON_IsString(timestamp_item) && (strlen(timestamp_item->valuestring) < sizeof(remote_v.timestamp))) {
            strcpy(remote_v.timestamp, timestamp_item->valuestring);
        }
    } else {
        logger_log(LOG_ERROR, "update.c : Erreur de parsing JSON pour version.json.");
    }

    cJSON_Delete(json);
    return remote_v;
}

/**
 * @brief Applique un fichier delta compressé à la base de données locale.
 * NOTE: C'est la partie la plus complexe. L'implémentation dépend fortement
 * de la structure exacte de vos fichiers de données et d'index.
 */
static int apply_demographics_delta(const char *delta_content, const char *data_dir, const char *demographics_filename) {
    logger_log(LOG_DEBUG, "update.c : Début de apply_demographics_delta");

    // Construire les chemins
    char csv_path[512];
    char tmp_path[512];
    snprintf(csv_path, sizeof(csv_path), "%s/%s", data_dir, demographics_filename);
    snprintf(tmp_path, sizeof(tmp_path), "%s/%s.tmp", data_dir, demographics_filename);

    // Ouvrir le fichier original en lecture
    logger_log(LOG_DEBUG, "update.c : Ouverture de %s en lecture...", csv_path);
    FILE *orig = fopen(csv_path, "r");
    if (!orig) {
        logger_log(LOG_ERROR, "update.c : Impossible d'ouvrir %s en lecture.", csv_path);
        return -1;
    }

    // Ouvrir le fichier temporaire en écriture
    logger_log(LOG_DEBUG, "update.c : Ouverture de %s en écriture...", tmp_path);
    FILE *tmp = fopen(tmp_path, "w");
    if (!tmp) {
        logger_log(LOG_ERROR, "update.c : Impossible de créer %s.", tmp_path);
        fclose(orig);
        return -1;
    }

    // Copier le contenu du fichier original dans le temporaire
    logger_log(LOG_DEBUG, "update.c : Début de la copie des datas vers le fichier temporaire.");
    char buf[4096];
    size_t n;
    while ((n = fread(buf, 1, sizeof(buf), orig)) > 0) {
        if (fwrite(buf, 1, n, tmp) != n) {
            logger_log(LOG_ERROR, "update.c : Erreur lors de la copie vers le fichier temporaire.");
            fclose(orig);
            fclose(tmp);
            remove(tmp_path);
            return -1;
        }
    }
    fclose(orig);

    // Ajouter les lignes du delta (parser delta_content ligne par ligne)
    logger_log(LOG_DEBUG, "update.c : Allocation mémoire pour le delta...");
    char *delta_copy = strdup(delta_content); // strtok modifie la chaîne
    if (!delta_copy) {
        logger_log(LOG_ERROR, "update.c : Allocation mémoire échouée pour le delta.");
        fclose(tmp);
        remove(tmp_path);
        return -1;
    }
    logger_log(LOG_DEBUG, "update.c : Ecriture du delta dans le fichier temporaire...");
    char *line = strtok(delta_copy, "\n");
    while (line) {
        if (fprintf(tmp, "%s\n", line) < 0) {
            logger_log(LOG_ERROR, "update.c : Erreur lors de l'écriture du delta dans le fichier temporaire.");
            free(delta_copy);
            fclose(tmp);
            remove(tmp_path);
            return -1;
        }
        line = strtok(NULL, "\n");
    }
    free(delta_copy);

    // Finaliser le fichier temporaire
    logger_log(LOG_DEBUG, "update.c : Fermeture du fichier temporaire...");
    if (fclose(tmp) != 0) {
        logger_log(LOG_ERROR, "update.c : Erreur lors de la fermeture du fichier temporaire.");
        remove(tmp_path);
        return -1;
    }

    // Remplacer l'original par le temporaire (atomique)
    logger_log(LOG_DEBUG, "update.c : Remplacement de %s par %s...", csv_path, tmp_path);
    if (rename(tmp_path, csv_path) != 0) {
        logger_log(LOG_ERROR, "update.c : Impossible de remplacer %s par %s.", csv_path, tmp_path);
        remove(tmp_path);
        return -1;
    }

    logger_log(LOG_INFO, "update.c : Delta appliqué avec succès.");
    return 0;
}

/**
 * @brief Lit le contenu d'un fichier .gz et le retourne sous forme de chaîne de caractères.
 * @note Cette fonction utilise zlib pour décompresser le fichier à la volée.
 * Il est nécessaire de lier le programme avec la bibliothèque zlib (ex: gcc -lz).
 */
char* read_gz_file_content(const char *file_path) {
    logger_log(LOG_DEBUG, "update.c : Début de read_gz_file_content pour %s", file_path);

    // Validation du chemin pour prévenir les attaques de path traversal
    if (validate_file_path(file_path) != 0) {
        logger_log(LOG_ERROR, "update.c : Chemin de fichier invalide ou dangereux: %s", file_path);
        return NULL;
    }

    // Utiliser gzopen pour les fichiers compressés
    gzFile gzfp = gzopen(file_path, "rb");
    if (gzfp == NULL) {
        logger_log(LOG_ERROR, "update.c : Impossible d'ouvrir le fichier %s", file_path);
        return NULL;
    }

    // Lire les données par petits morceaux pour déterminer la taille totale
    // car on ne peut pas utiliser fseek/ftell de la même manière qu'avec un fichier non compressé
    size_t buffer_size = 1024;
    size_t total_size = 0;
    char *content = NULL;
    char buffer[buffer_size];
    int bytes_read;

    logger_log(LOG_DEBUG, "update.c : Lecture du fichier %s...", file_path);
    while ((bytes_read = gzread(gzfp, buffer, buffer_size)) > 0) {
        // Redimensionner la mémoire allouée et copier le nouveau morceau
        char *temp_content = realloc(content, total_size + bytes_read + 1);
        if (temp_content == NULL) {
            logger_log(LOG_ERROR, "update.c : Échec de l'allocation mémoire pour %s", file_path);
            free(content);
            gzclose(gzfp);
            return NULL;
        }
        content = temp_content;
        memcpy(content + total_size, buffer, bytes_read);
        total_size += bytes_read;
    }

    // Vérifier s'il y a eu une erreur de lecture
    logger_log(LOG_DEBUG, "update.c : Fin de la lecture du fichier %s, total lu: %zu octets", file_path, total_size);
    if (bytes_read < 0) {
        logger_log(LOG_ERROR, "update.c : Erreur de lecture du fichier %s", file_path);
        free(content);
        gzclose(gzfp);
        return NULL;
    } 

    // Assurez-vous que la chaîne se termine par un caractère nul
    logger_log(LOG_DEBUG, "update.c : Allocation mémoire pour le contenu décompressé...");
    if (content) {
        content[total_size] = '\0';
    } else {
        content = malloc(1);
        if (content) {
            content[0] = '\0';
        }
    }

    logger_log(LOG_DEBUG, "update.c : Fermeture du fichier %s", file_path);
    gzclose(gzfp);
    
    logger_log(LOG_INFO, "update.c : Lecture du fichier %s réussie (%zu octets).", file_path, total_size);
    return content;
}

/**
 * @brief Valide un chemin de fichier pour prévenir les attaques de path traversal
 * @param file_path Le chemin du fichier à valider
 * @return 0 si le chemin est valide, -1 sinon
 */
int validate_file_path(const char *file_path) {
    if (!file_path) {
        logger_log(LOG_ERROR, "update.c : validate_file_path - Chemin de fichier NULL");
        return -1;
    }

    // Vérifier la longueur du chemin
    size_t path_len = strlen(file_path);
    if (path_len == 0 || path_len >= PATH_MAX) {
        logger_log(LOG_ERROR, "update.c : validate_file_path - Longueur de chemin invalide: %zu", path_len);
        return -1;
    }

    // Résoudre le chemin canonique pour détecter les path traversal malveillants
    char resolved_path[PATH_MAX];
    char *resolved = realpath(file_path, resolved_path);

    if (resolved == NULL) {
        // Si realpath échoue, le fichier n'existe peut-être pas encore, ce qui peut être normal
        if (errno == ENOENT) {
            logger_log(LOG_DEBUG, "update.c : validate_file_path - Fichier n'existe pas encore: %s", file_path);
            // Pour les fichiers qui n'existent pas encore, on fait une validation basique
            // Vérifier les séquences dangereuses multiples qui indiquent clairement une attaque
            if (strstr(file_path, "../../") != NULL ||
                strstr(file_path, "../../../") != NULL ||
                strstr(file_path, "../../../../") != NULL) {
                logger_log(LOG_ERROR, "update.c : validate_file_path - Séquences de path traversal multiples détectées: %s", file_path);
                fprintf(stderr, "ERREUR SÉCURITÉ: Séquences de path traversal multiples détectées dans le chemin: %s\n", file_path);
                return -1;
            }
        } else {
            logger_log(LOG_ERROR, "update.c : validate_file_path - Impossible de résoudre le chemin %s: %s",
                      file_path, strerror(errno));
            fprintf(stderr, "ERREUR SÉCURITÉ: Impossible de résoudre le chemin: %s\n", file_path);
            return -1;
        }
    } else {
        // Vérifier que le chemin résolu ne sort pas de l'arborescence attendue
        char cwd[PATH_MAX];
        if (getcwd(cwd, sizeof(cwd)) != NULL) {
            // Obtenir le répertoire parent du répertoire de travail actuel pour permettre l'accès aux fichiers de config
            char parent_cwd[PATH_MAX];
            strncpy(parent_cwd, cwd, sizeof(parent_cwd) - 1);
            parent_cwd[sizeof(parent_cwd) - 1] = '\0';
            char *last_slash = strrchr(parent_cwd, '/');
            if (last_slash && last_slash != parent_cwd) {
                *last_slash = '\0';
            }

            // Vérifier que le chemin résolu ne sort pas complètement de l'arborescence de projet
            if ((strstr(resolved_path, "/etc/") != NULL ||
                 strstr(resolved_path, "/root/") != NULL ||
                 strstr(resolved_path, "/usr/") != NULL ||
                 strstr(resolved_path, "/var/") != NULL ||
                 strstr(resolved_path, "/tmp/") != NULL) &&
                strstr(resolved_path, parent_cwd) == NULL) {
                logger_log(LOG_ERROR, "update.c : validate_file_path - Tentative d'accès à un répertoire système: %s", resolved_path);
                fprintf(stderr, "ERREUR SÉCURITÉ: Tentative d'accès à un répertoire système: %s\n", resolved_path);
                return -1;
            }
        }
        logger_log(LOG_DEBUG, "update.c : validate_file_path - Chemin résolu: %s", resolved_path);
    }

    // Vérifier les caractères de contrôle et autres caractères dangereux
    for (size_t i = 0; i < path_len; i++) {
        unsigned char c = (unsigned char)file_path[i];
        if (c < 32 && c != '\t') {  // Caractères de contrôle sauf tab
            logger_log(LOG_ERROR, "update.c : validate_file_path - Caractère de contrôle détecté à la position %zu", i);
            fprintf(stderr, "ERREUR SÉCURITÉ: Caractère de contrôle détecté dans le chemin à la position %zu\n", i);
            return -1;
        }
    }



    logger_log(LOG_DEBUG, "update.c : validate_file_path - Chemin validé avec succès: %s", file_path);
    return 0;
}

/**
 * @brief Lit le contenu d'un fichier et le retourne sous forme de chaîne de caractères.
 */
char* read_file_content(const char *file_path) {
    logger_log(LOG_DEBUG, "update.c : Début de read_file_content pour %s", file_path);

    // Validation du chemin pour prévenir les attaques de path traversal
    if (validate_file_path(file_path) != 0) {
        logger_log(LOG_ERROR, "update.c : Chemin de fichier invalide ou dangereux: %s", file_path);
        return NULL;
    }

    FILE *fp = fopen(file_path, "r");
    if (fp == NULL) {
        logger_log(LOG_ERROR, "update.c : Impossible d'ouvrir le fichier %s", file_path);
        return NULL;
    }

    // Déterminer la taille du fichier
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);

    char *content = malloc(file_size + 1);
    if (content == NULL) {
        logger_log(LOG_ERROR, "update.c : Échec de l'allocation mémoire pour %s", file_path);
        fclose(fp);
        return NULL;
    }

    // Lire le contenu du fichier et s'assurer qu'il se termine par un caractère nul
    size_t bytes_read = fread(content, 1, file_size, fp);
    if (bytes_read != file_size) {
        logger_log(LOG_ERROR, "update.c : Erreur de lecture du fichier %s", file_path);
        free(content);
        fclose(fp);
        return NULL;
    }
    content[file_size] = '\0';
    fclose(fp);

    logger_log(LOG_INFO, "update.c : Lecture du fichier %s réussie (%ld octets).", file_path, file_size);
    return content;
}

/**
 * @brief Fonction utilitaire pour décompresser un flux de données Gzip en mémoire.
 * NOTE: Cette fonction nécessite la bibliothèque zlib.
 */
static int decompress_gzip_content(const char *compressed_data, size_t compressed_size, char **decompressed_data, size_t *decompressed_size) {
    logger_log(LOG_DEBUG, "update.c : Début de decompress_gzip_content");

    if (!compressed_data || !decompressed_data || !decompressed_size) {
        logger_log(LOG_ERROR, "update.c : Paramètres invalides pour decompress_gzip_content.");
        return -1;
    }

    // Taille initiale estimée pour la sortie (on commence à 4x la taille compressée)
    size_t out_alloc = compressed_size * 4;
    *decompressed_data = malloc(out_alloc);
    if (!*decompressed_data) {
        logger_log(LOG_ERROR, "update.c : Allocation mémoire échouée pour la décompression.");
        return -1;
    }

    z_stream strm = {0};
    strm.next_in = (Bytef *)compressed_data;
    strm.avail_in = compressed_size;
    strm.next_out = (Bytef *)*decompressed_data;
    strm.avail_out = out_alloc;

    // 16+MAX_WBITS pour indiquer un flux gzip
    if (inflateInit2(&strm, 16 + MAX_WBITS) != Z_OK) {
        logger_log(LOG_ERROR, "update.c : Échec de inflateInit2.");
        free(*decompressed_data);
        *decompressed_data = NULL;
        return -1;
    }

    int ret;
    while ((ret = inflate(&strm, Z_NO_FLUSH)) == Z_OK) {
        if (strm.avail_out == 0) {
            // Besoin de plus de place
            size_t old_size = out_alloc;
            out_alloc *= 2;
            char *tmp = realloc(*decompressed_data, out_alloc);
            if (!tmp) {
                logger_log(LOG_ERROR, "update.c : realloc échoué pendant la décompression.");
                inflateEnd(&strm);
                free(*decompressed_data);
                *decompressed_data = NULL;
                return -1;
            }
            *decompressed_data = tmp;
            strm.next_out = (Bytef *)(*decompressed_data + old_size);
            strm.avail_out = out_alloc - old_size;
        }
    }

    logger_log(LOG_DEBUG, "update.c : Fin de la décompression, code de retour: %d", ret);
    if (ret != Z_STREAM_END) {
        logger_log(LOG_ERROR, "update.c : Erreur lors de la décompression Gzip (code %d).", ret);
        inflateEnd(&strm);
        free(*decompressed_data);
        *decompressed_data = NULL;
        return -1;
    }

    *decompressed_size = strm.total_out;
    // Ajout d'un caractère nul pour faciliter l'utilisation comme chaîne
    (*decompressed_data)[*decompressed_size] = '\0';

    inflateEnd(&strm);
    logger_log(LOG_DEBUG, "update.c : Décompression réussie (%zu octets).", *decompressed_size);
    return 0;
}


/**
 * @brief Compresse un fichier en Gzip (comme l'inverse de decompress_gzip_content)
 * @param src_path Chemin vers le fichier source non compressé
 * @param dest_path Chemin vers le fichier gzip de sortie
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int compress_file_in_gzip(const char *src_path, const char *dest_path) {
    if (!src_path || !dest_path) {
        logger_log(LOG_ERROR, "compress_file_in_gzip : paramètres invalides");
        return -1;
    }

    // Validation des chemins pour prévenir les attaques de path traversal
    if (validate_file_path(src_path) != 0) {
        logger_log(LOG_ERROR, "compress_file_in_gzip : Chemin source invalide ou dangereux: %s", src_path);
        return -1;
    }

    if (validate_file_path(dest_path) != 0) {
        logger_log(LOG_ERROR, "compress_file_in_gzip : Chemin destination invalide ou dangereux: %s", dest_path);
        return -1;
    }

    FILE *src = fopen(src_path, "rb");
    if (!src) {
        logger_log(LOG_ERROR, "compress_file_in_gzip : Impossible d'ouvrir %s : %s",
                   src_path, strerror(errno));
        return -1;
    }

    gzFile dest = gzopen(dest_path, "wb");
    if (!dest) {
        logger_log(LOG_ERROR, "compress_file_in_gzip : Impossible de créer %s",
                   dest_path);
        fclose(src);
        return -1;
    }

    char buffer[8192];
    size_t bytes;
    while ((bytes = fread(buffer, 1, sizeof(buffer), src)) > 0) {
        if (gzwrite(dest, buffer, (unsigned)bytes) != (int)bytes) {
            logger_log(LOG_ERROR, "compress_file_in_gzip : erreur écriture gzip vers %s",
                       dest_path);
            gzclose(dest);
            fclose(src);
            return -1;
        }
    }

    fclose(src);
    gzclose(dest);
    logger_log(LOG_INFO, "compress_file_in_gzip : %s compressé en %s avec succès",
               src_path, dest_path);
    return 0;
}


int rebuild_demographics_index(const char *data_dir, const char *demographics_filename, const char *index_filename, int key_column) {
    logger_log(LOG_DEBUG, "Début de rebuild_demographics_index...");

    // Validation des paramètres d'entrée pour prévenir les attaques de path traversal
    if (validate_file_path(data_dir) != 0) {
        logger_log(LOG_ERROR, "rebuild_demographics_index : Répertoire de données invalide ou dangereux: %s", data_dir);
        return -1;
    }

    // Validation des noms de fichiers (pas de path traversal dans les noms)
    if (strstr(demographics_filename, "../") != NULL || strstr(demographics_filename, "..\\") != NULL ||
        strstr(index_filename, "../") != NULL || strstr(index_filename, "..\\") != NULL) {
        logger_log(LOG_ERROR, "rebuild_demographics_index : Nom de fichier contenant des séquences dangereuses");
        return -1;
    }

    char data_path[512], index_path[512], tmp_path[512];
    snprintf(data_path, sizeof(data_path), "%s/%s", data_dir, demographics_filename);
    snprintf(index_path, sizeof(index_path), "%s/%s", data_dir, index_filename);
    snprintf(tmp_path, sizeof(tmp_path), "%s/%s.tmp", data_dir, index_filename);

    // Validation des chemins construits
    if (validate_file_path(data_path) != 0 || validate_file_path(index_path) != 0 || validate_file_path(tmp_path) != 0) {
        logger_log(LOG_ERROR, "rebuild_demographics_index : Un ou plusieurs chemins construits sont invalides");
        return -1;
    }

    logger_log(LOG_DEBUG, "Ouverture du fichier de données %s", data_path);
    FILE *data = fopen(data_path, "r");
    if (!data) {
        logger_log(LOG_ERROR, "Impossible d'ouvrir le fichier data %s", data_path);
        return -1;
    }
    
    logger_log(LOG_DEBUG, "Ouverture du fichier d'index %s", tmp_path);
    FILE *idx = fopen(tmp_path, "w");
    if (!idx) {
        logger_log(LOG_ERROR, "Impossible de créer le fichier index %s", tmp_path);
        fclose(data);
        return -1;
    }

    // Choix de l'entête selon le type d'index
    logger_log(LOG_DEBUG, "Construction de l'entête du fichier d'index %s", tmp_path);
    const char *header = "iep;byte_position;ligne_length;ligne_numero\n";
    if (key_column == 1) {
        header = "ipp;byte_position;ligne_length;ligne_numero\n";
    } else if (key_column == 2) {
        header = "numero_ss;byte_position;ligne_length;ligne_numero\n";
    }
    fprintf(idx, "%s", header);
    logger_log(LOG_DEBUG, "Entête retenue : %s", header);

    logger_log(LOG_DEBUG, "Reconstruction de l'index...");
    char line[4096];
    long byte_pos = 0;
    int line_num = 0;
    while (fgets(line, sizeof(line), data)) {
        size_t len = strlen(line);
        if (len > 0 && line[len-1] == '\n') line[len-1] = '\0';

        // Extraire la clé selon la colonne demandée
        char key[128] = "";
        char *tok = strtok(line, ";");
        int col = 0;
        while (tok && col <= key_column) {
            if (col == key_column) strncpy(key, tok, sizeof(key)-1);
            tok = strtok(NULL, ";");
            col++;
        }

        fprintf(idx, "%s;%ld;%zu;%d\n", key, byte_pos, len, line_num);

        byte_pos += len + 1; // +1 pour le \n
        line_num++;
    }

    logger_log(LOG_DEBUG, "Fin de la reconstruction de l'index. Fermeture du fichier de données %s", data_path);
    fclose(data);
    if (fclose(idx) != 0) {
        logger_log(LOG_ERROR, "Erreur lors de la fermeture du fichier index temporaire.");
        remove(tmp_path);
        return -1;
    }

    logger_log(LOG_DEBUG, "Remplacement de l'index %s par %s", index_path, tmp_path);
    if (rename(tmp_path, index_path) != 0) {
        logger_log(LOG_ERROR, "Impossible de remplacer l'index %s", index_path);
        remove(tmp_path);
        return -1;
    }

    logger_log(LOG_INFO, "Index %s reconstruit avec succès.", index_filename);
    return 0;
}


