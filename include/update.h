#ifndef UPDATE_H
#define UPDATE_H

#include "sftp_manager.h"  // Inclusion du nouveau module SFTP

// Structure pour la version locale
typedef struct {
    int current_version;
    char last_sync[32];
    char last_status[16];
} LocalVersion;

// Structure pour la version distante
typedef struct {
    int current_version;
    int snapshot;
    char timestamp[32];
} RemoteVersion;

/**
 * @brief Point d'entrée de la logique de mise à jour avec support SFTP.
 * Cette fonction orchestre le processus complet de vérification et d'application
 * des mises à jour de la démographie, incluant le téléchargement via SFTP si nécessaire.
 * @param local_data_dir Chemin vers le répertoire local des données.
 * @param shared_dir Chemin vers le répertoire partagé contenant les mises à jour.
 * @param local_version_filename Nom du fichier de version locale.
 * @param remote_version_filename Nom du fichier de version distante.
 * @param demographics_filename Nom du fichier de démographie.
 * @param index_iep_filename Nom du fichier d'index IEP.
 * @param index_ipp_filename Nom du fichier d'index IPP.
 * @param index_numss_filename Nom du fichier d'index NUMSS.
 * @param sftp_config Configuration SFTP (peut être NULL pour désactiver SFTP).
 * @return 0 en cas de succès, -1 en cas d'erreur.
 */
int run_update_process_with_sftp(const char *local_data_dir, const char *shared_dir, 
                                const char *local_version_filename, const char *remote_version_filename, 
                                const char *demographics_filename, const char *index_iep_filename, 
                                const char *index_ipp_filename, const char *index_numss_filename,
                                const SftpConfig *sftp_config);

/**
 * @brief Reconstruit l'index des données démographiques.
 * @param data_dir Répertoire contenant les données.
 * @param demographics_filename Nom du fichier de démographie.
 * @param index_filename Nom du fichier d'index à créer.
 * @param key_column Colonne à utiliser comme clé (0=IEP, 1=IPP, 2=NUMSS).
 * @return 0 en cas de succès, -1 en cas d'erreur.
 */
int rebuild_demographics_index(const char *data_dir, const char *demographics_filename, 
                              const char *index_filename, int key_column);


/**
 * @brief Compresse un fichier en Gzip (inverse de decompress_gzip_content).
 * @param src_path Chemin vers le fichier source non compressé
 * @param dest_path Chemin vers le fichier gzip de sortie
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int compress_file_in_gzip(const char *src_path, const char *dest_path);

/**
 * @brief Valide un chemin de fichier pour prévenir les attaques de path traversal
 * @param file_path Le chemin du fichier à valider
 * @return 0 si le chemin est valide, -1 sinon
 */
int validate_file_path(const char *file_path);

#endif // UPDATE_H


