#!/bin/bash

echo "==================================================="
echo "Test de Sécurité - Protection Path Traversal"
echo "Cas 1: Configuration malveillante (argv[1])"
echo "Cas 2: Chemins SFTP malveillants (via config)"
echo "==================================================="

cd build

echo ""
echo "1. Test avec chemin valide (devrait fonctionner jusqu'à l'erreur de répertoire):"
echo "   ./demog_updater ../config/config.json"
timeout 3 ./demog_updater ../config/config.json
echo "   → Code de retour: $?"

echo ""
echo "2. Test avec path traversal simple (devrait être bloqué):"
echo "   ./demog_updater ../../../etc/passwd"
./demog_updater ../../../etc/passwd
echo "   → Code de retour: $?"

echo ""
echo "3. Test avec path traversal complexe (devrait être bloqué):"
echo "   ./demog_updater ../../../../../../etc/shadow"
./demog_updater ../../../../../../etc/shadow
echo "   → Code de retour: $?"

echo ""
echo "4. Test avec chemin absolu vers fichier système (devrait être bloqué):"
echo "   ./demog_updater /etc/passwd"
./demog_updater /etc/passwd
echo "   → Code de retour: $?"

echo ""
echo "5. Test avec chemin contenant des caractères de contrôle:"
echo "   ./demog_updater $'../config/config.json\\x00'"
./demog_updater $'../config/config.json\x00'
echo "   → Code de retour: $?"

echo ""
echo "6. Test de validation des chemins SFTP:"
echo "   ./test_sftp_security"
./test_sftp_security

echo ""
echo "==================================================="
echo "Résumé des Protections:"
echo "- CAS 1: Fichiers de config malveillants → Bloqués"
echo "- CAS 2: Chemins SFTP avec path traversal → Bloqués"
echo "- Tous les chemins dangereux → Code de retour 1"
echo "- Chemins légitimes → Fonctionnent normalement"
echo "==================================================="
