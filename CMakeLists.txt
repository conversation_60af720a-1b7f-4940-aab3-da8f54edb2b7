# Version minimale de CMake
cmake_minimum_required(VERSION 3.10)

# Nom et version du projet
project(DemogUpdater VERSION 1.0.20250811.1603)

# Répertoires d'includes locaux
include_directories(include)

# Fichiers sources
set(SOURCES
    src/main.c
    src/update.c
    src/core/logger.c
    src/sftp_manager.c   
)

# Recherche des bibliothèques requises
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)

# Recherche de libssh2 via pkg-config
pkg_check_modules(LIBSSH2 REQUIRED libssh2)

# Création de l'exécutable
add_executable(demog_updater ${SOURCES})

# Configuration des include directories
target_include_directories(demog_updater PRIVATE ${LIBSSH2_INCLUDE_DIRS})

# Lien des bibliothèques nécessaires
target_link_libraries(demog_updater PRIVATE 
    cjson 
    z 
    OpenSSL::Crypto    # Pour les fonctions MD5/EVP
    ${LIBSSH2_LIBRARIES}  # Pour libssh2
)

# Flags de compilation pour libssh2 si nécessaire
target_compile_options(demog_updater PRIVATE ${LIBSSH2_CFLAGS_OTHER})

# ===============================
# CONFIGURATION DE L'INSTALLATION
# ===============================

if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "/opt/demog_updater" CACHE PATH "Installation directory" FORCE)
endif()

# Installation binaire
install(TARGETS demog_updater
    RUNTIME DESTINATION bin
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)

# Installation du fichier de config
install(FILES config/config.json
    DESTINATION etc
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)

# Répertoires de données et logs
install(DIRECTORY DESTINATION var/data
    DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)
install(DIRECTORY DESTINATION var/logs
    DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)
install(DIRECTORY DESTINATION var/shared
    DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)

# README / Documentation
install(FILES README.md
    DESTINATION share/doc/demog_updater
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)

# Résumé config installation
message(STATUS "=== Configuration d'installation ===")
message(STATUS "Préfixe d'installation: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Exécutable: ${CMAKE_INSTALL_PREFIX}/bin")
message(STATUS "Configuration: ${CMAKE_INSTALL_PREFIX}/etc")
message(STATUS "Données: ${CMAKE_INSTALL_PREFIX}/var/")
message(STATUS "Documentation: ${CMAKE_INSTALL_PREFIX}/share/doc/demog_updater")
message(STATUS "Bibliothèques liées: cjson, zlib, OpenSSL::Crypto, libssh2")