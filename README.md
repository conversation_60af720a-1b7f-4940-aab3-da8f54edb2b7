# demog_updater

**demog_updater** est un service Linux permettant de synchroniser et mettre à jour automatiquement des fichiers de données démographiques à partir d’un serveur SFTP ou d’un dossier partagé local.  
Il gère les versions, applique les deltas, reconstruit les index et journalise toutes les opérations.

## Fonctionnalités

- Téléchargement sécurisé des fichiers de version et des deltas via SFTP (libssh2)
- Application automatique des mises à jour de données (deltas CSV.gz)
- Reconstruction des fichiers d’index (IEP, IPP, Numéro SS)
- Journalisation détaillée (fichier de logs)
- Configuration flexible via fichier JSON
- Fonctionnement planifié (jours/heures)

## Structure du projet

```
demog_updater/
├── app/
│   ├── data/
│   ├── logs/
│   └── shared/
├── include/
│   ├── sftp_manager.h
│   ├── update.h
│   └── core/
│       └── logger.h
├── src/
│   ├── main.c
│   ├── update.c
│   ├── sftp_manager.c
│   └── core/
│       └── logger.c
├── config/
│   └── config.json
├── CMakeLists.txt
├── deploy.sh
├── Dockerfile
├── README.md
└── structure.txt
```

## Dépendances

- **libssh2** (pour SFTP)
- **zlib** (pour la décompression gzip)
- **cJSON** (pour le parsing JSON)
- Un compilateur C (gcc recommandé)
- CMake (pour la compilation)

## Compilation

```sh
sudo apt install build-essential cmake libssh2-1-dev zlib1g-dev libcjson-dev
mkdir build
cd build
cmake ..
make
```

Le binaire sera généré dans `build/`.

## Configuration

Le fichier `config/config.json` permet de paramétrer :

- Chemins des dossiers de données, logs, partagés
- Noms des fichiers de version et de données
- Plages horaires et jours de fonctionnement
- Paramètres SFTP (hôte, port, utilisateur, clé privée, etc.)
- Niveau de log

**Exemple de section SFTP :**
```json
"sftp": {
    "enabled": true,
    "host": "sftp.example.com",
    "port": 22,
    "username": "user",
    "password": "pass",
    "private_key_path": "/chemin/vers/id_rsa",
    "remote_base_path": "/remote/dir",
    "connection_timeout": 30,
    "transfer_timeout": 300,
    "retry_attempts": 3,
    "retry_delay": 5
}
```

## Utilisation

Lancer le service avec :

```sh
./demog_updater config/config.json
```

Le service tourne en boucle, vérifie les mises à jour selon la configuration, télécharge les fichiers nécessaires via SFTP si activé, applique les deltas et reconstruit les index.

## Journalisation

Les logs sont écrits dans le dossier spécifié par `log_dir_path` dans la configuration.  
Le niveau de log est configurable (`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`).

## Déploiement

Un script `deploy.sh` et un `Dockerfile` sont fournis pour faciliter le déploiement.

## Auteurs

- Projet développé par Patrick Paysan.

## Licence

Ce projet est sous licence Patrick Paysan.

---
