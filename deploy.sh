#!/bin/bash

# Script de déploiement automatisé pour demog_updater
# Usage: ./deploy.sh

set -e

# Couleurs pour l'affichage
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# --- Variables de configuration ---
PROJECT_NAME="demog_updater"
PROJECT_DIR="/home/<USER>/vscode/demog_updater"
PARENT_DIR="/home/<USER>/vscode"
BUILD_DIR="$PROJECT_DIR/build"
MAIN_C_FILE="$PROJECT_DIR/src/main.c"
TAR_ARCHIVE_NAME="demog_updater.tar.gz"
TAR_ARCHIVE_PATH="$PARENT_DIR/$TAR_ARCHIVE_NAME"
SHARED_DATA_HOST="/opt/shared-data"
DATA_SOURCE_DIR="$PROJECT_DIR/app/data"

# Vérifier qu'on est dans le bon répertoire
if [ ! -f "$MAIN_C_FILE" ]; then
    print_error "Fichier main.c non trouvé dans $PROJECT_DIR/src/. Assurez-vous d'être dans le bon répertoire."
    exit 1
fi

# 0. Préparer le dossier shared-data sur l'host
print_step "Préparation du dossier partagé sur l'host : $SHARED_DATA_HOST"

# Créer le dossier s'il n'existe pas
if [ ! -d "$SHARED_DATA_HOST" ]; then
    print_warning "Dossier $SHARED_DATA_HOST n'existe pas, création avec sudo..."
    if ! sudo mkdir -p "$SHARED_DATA_HOST"; then
        print_error "Échec de la création du dossier $SHARED_DATA_HOST."
        exit 1
    fi
    echo "Dossier $SHARED_DATA_HOST créé."
fi

# Vérifier et copier les fichiers de données de développement
if [ -d "$DATA_SOURCE_DIR" ]; then
    print_step "Copie des fichiers de données depuis $DATA_SOURCE_DIR vers $SHARED_DATA_HOST"
    
    # Copier les fichiers avec sudo si nécessaire
    if ! sudo cp -r "$DATA_SOURCE_DIR"/* "$SHARED_DATA_HOST/"; then
        print_error "Échec de la copie des fichiers de données."
        exit 1
    fi
    
    # Ajuster les permissions pour permettre l'accès aux conteneurs
    if ! sudo chown -R $USER:$USER "$SHARED_DATA_HOST"; then
        print_warning "Impossible de changer le propriétaire, tentative de modification des permissions..."
        sudo chmod -R 755 "$SHARED_DATA_HOST"
    fi
    
    echo "Fichiers de données copiés et permissions ajustées."
    
    # Lister les fichiers copiés pour vérification
    print_step "Vérification des fichiers dans $SHARED_DATA_HOST :"
    ls -la "$SHARED_DATA_HOST"
else
    print_warning "Dossier source $DATA_SOURCE_DIR non trouvé. Les fichiers de données ne seront pas copiés."
fi

# 1. Mettre à jour la version dans main.c
print_step "Mise à jour de la version..."
NEW_VERSION="1.0.$(date +%Y%m%d.%H%M)"
if ! sed -i "s/#define VERSION \".*\"/#define VERSION \"$NEW_VERSION\"/" "$MAIN_C_FILE"; then
    print_error "Échec de la mise à jour de la version dans $MAIN_C_FILE."
    exit 1
fi
echo "Version mise à jour : $NEW_VERSION"

# 2. Aller au répertoire parent
print_step "Navigation vers le répertoire parent : $PARENT_DIR"
cd "$PARENT_DIR" || { print_error "Impossible de naviguer vers $PARENT_DIR."; exit 1; }

# 3. Supprimer le dossier build s'il existe
if [ -d "$BUILD_DIR" ]; then
    print_step "Suppression du dossier build : $BUILD_DIR"
    if ! rm -rf "$BUILD_DIR"; then
        print_error "Échec de la suppression du dossier build."
        exit 1
    fi
    echo "Dossier build supprimé"
else
    print_warning "Dossier build n'existe pas ($BUILD_DIR), rien à supprimer."
fi

# 4. Supprimer l'ancienne archive si elle existe
if [ -f "$TAR_ARCHIVE_PATH" ]; then
    print_step "Suppression de l'ancienne archive $TAR_ARCHIVE_NAME..."
    if ! rm -f "$TAR_ARCHIVE_PATH"; then
        print_error "Échec de la suppression de l'ancienne archive."
        exit 1
    fi
    echo "Ancienne archive supprimée."
fi

# 5. Créer l'archive tar.gz avec exclusions (en excluant aussi app/data maintenant)
print_step "Création de l'archive tar.gz : $TAR_ARCHIVE_NAME"
if ! tar -czf "$TAR_ARCHIVE_NAME" \
    --exclude="demog_updater/build" \
    --exclude="demog_updater/structure.txt" \
    --exclude="demog_updater/.vscode" \
    --exclude="demog_updater/.idea" \
    --exclude="demog_updater/deploy.sh" \
    --exclude="demog_updater/*.tar.gz" \
    --exclude="demog_updater/app/data" \
    demog_updater/; then
    print_error "Échec de la création de l'archive tar.gz."
    exit 1
fi
echo "Archive créée : $TAR_ARCHIVE_NAME (sans le dossier app/data)"

# 6. Changer les permissions
print_step "Modification des permissions pour $TAR_ARCHIVE_NAME..."
if ! chmod 777 "$TAR_ARCHIVE_NAME"; then
    print_error "Échec de la modification des permissions sur $TAR_ARCHIVE_NAME."
    exit 1
fi
echo "Permissions changées pour 777."

# 7. Déplacer l'archive dans le dossier du projet
print_step "Déplacement de l'archive $TAR_ARCHIVE_NAME vers $PROJECT_DIR/"
if ! mv "$TAR_ARCHIVE_NAME" "$PROJECT_DIR/"; then
    print_error "Échec du déplacement de l'archive."
    exit 1
fi
echo "Archive déplacée."

# 8. Retourner dans le dossier du projet
print_step "Retour dans le dossier du projet : $PROJECT_DIR"
cd "$PROJECT_DIR" || { print_error "Impossible de retourner dans $PROJECT_DIR."; exit 1; }

# 9. Build du conteneur Podman
print_step "Build du conteneur Podman..."
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile non trouvé dans $PROJECT_DIR. Impossible de construire l'image Podman."
    exit 1
fi
if ! podman build -t demog_updater:latest -f Dockerfile .; then
    print_error "Échec du build du conteneur Podman."
    exit 1
fi
echo "Conteneur Podman demog_updater:latest construit avec succès."

# 10. Supprimer les images Podman orphelines
print_step "Suppression des images Podman orphelines (<none> <none>)..."
DANGLING_IMAGES=$(podman images -f "dangling=true" -q)
if [ -n "$DANGLING_IMAGES" ]; then
    print_warning "Images orphelines trouvées et en cours de suppression : $(echo $DANGLING_IMAGES | wc -w) images."
    if ! podman rmi -f $DANGLING_IMAGES; then
        print_error "Échec de la suppression de certaines images orphelines."
        # Ne pas sortir ici, car le déploiement principal est réussi
    fi
    echo "Images orphelines supprimées."
else
    print_warning "Aucune image orpheline à supprimer."
fi

print_step "Déploiement terminé avec succès !"
echo -e "${GREEN}Version déployée : $NEW_VERSION${NC}"

# Afficher la taille de l'archive finale
ARCHIVE_SIZE=$(du -h "$PROJECT_DIR/$TAR_ARCHIVE_NAME" | cut -f1)
echo -e "${GREEN}Taille de l'archive : $ARCHIVE_SIZE${NC}"
echo -e "${GREEN}Image Podman taggée : demog_updater:latest${NC}"

# Afficher les informations sur le dossier partagé
echo ""
echo "=== Informations sur les données partagées ==="
echo -e "${GREEN}Dossier partagé sur l'host : $SHARED_DATA_HOST${NC}"
echo -e "${GREEN}Fichiers disponibles :${NC}"
if [ -d "$SHARED_DATA_HOST" ]; then
    ls -la "$SHARED_DATA_HOST"
else
    echo "Aucun fichier trouvé"
fi

# Après la construction de l'image, ajouter un exemple de lancement
echo ""
echo "=== Commande de lancement recommandée ==="
echo "podman run -d --name demog_updater \\"
echo "  -v $SHARED_DATA_HOST:/opt/demog_updater/var/data:Z \\"
echo "  -v ./logs:/opt/demog_updater/var/logs:Z \\"
echo "  demog_updater:latest"
echo "podman run -d --name demogupdater_service -v /opt/shared-data:/opt/demog_updater/var/data:Z -v /var/log/demogupdater/:/opt/demog_updater/var/logs:Z demog_updater:latest"